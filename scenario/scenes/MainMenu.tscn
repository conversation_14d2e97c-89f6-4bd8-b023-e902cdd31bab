[gd_scene load_steps=11 format=3 uid="uid://bx8vn7qkqxqxq"]

[ext_resource type="Script" path="res://scripts/MainMenu.gd" id="1_main"]
[ext_resource type="Texture2D" path="res://assets/generated/menu_background_main.png" id="2_background"]
[ext_resource type="Texture2D" path="res://assets/generated/menu_panel_main.png" id="3_panel"]
[ext_resource type="Texture2D" path="res://assets/generated/menu_van_helsing_portrait.png" id="4_portrait"]
[ext_resource type="Texture2D" path="res://assets/generated/menu_title_frame.png" id="5_title_frame"]
[ext_resource type="Texture2D" path="res://assets/generated/menu_button_nova_hra.png" id="6_button_nova"]
[ext_resource type="Texture2D" path="res://assets/generated/menu_button_kapitoly.png" id="7_button_kapitoly"]
[ext_resource type="Texture2D" path="res://assets/generated/menu_button_nastavenia.png" id="8_button_nastavenia"]
[ext_resource type="Texture2D" path="res://assets/generated/menu_button_o_hre.png" id="9_button_o_hre"]
[ext_resource type="Texture2D" path="res://assets/generated/menu_decoration_horizontal.png" id="10_decoration"]
[ext_resource type="FontFile" path="res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf" id="11_font"]


[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_background")
expand_mode = 1
stretch_mode = 6

# Hlavný nadpis - dramaticky väčší a vyššie umiestnený
[node name="MainTitleContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -350.0
offset_right = 400.0
offset_bottom = -200.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="Label" parent="MainTitleContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1.0, 0.843, 0.0, 1.0)
theme_override_colors/font_shadow_color = Color(0.0, 0.0, 0.0, 0.8)
theme_override_colors/font_outline_color = Color(0.4, 0.0, 0.0, 1.0)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 3
theme_override_constants/outline_size = 3
theme_override_fonts/font = ExtResource("11_font")
theme_override_font_sizes/font_size = 48
text = "PREKLIATE DEDICSTVO"
horizontal_alignment = 1
vertical_alignment = 1

[node name="SubtitleLabel" type="Label" parent="MainTitleContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 0.9)
theme_override_colors/font_shadow_color = Color(0.0, 0.0, 0.0, 0.7)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 1
theme_override_fonts/font = ExtResource("11_font")
theme_override_font_sizes/font_size = 20
text = "Van Helsing: Goticka dobrodruzna hra"
horizontal_alignment = 1
vertical_alignment = 1

[node name="VersionLabel" type="Label" parent="MainTitleContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.7, 0.6, 0.4, 0.7)
theme_override_colors/font_shadow_color = Color(0.0, 0.0, 0.0, 0.5)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 0.6)
theme_override_constants/shadow_offset_x = 1
theme_override_constants/shadow_offset_y = 1
theme_override_constants/outline_size = 1
theme_override_fonts/font = ExtResource("11_font")
theme_override_font_sizes/font_size = 14
text = "Verzia 1.0.0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="VanHelsingPortrait" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 50.0
offset_top = -200.0
offset_right = 354.0
offset_bottom = 200.0
grow_vertical = 2
texture = ExtResource("4_portrait")
expand_mode = 1
stretch_mode = 5

[node name="MainPanel" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -450.0
offset_top = -300.0
offset_right = -50.0
offset_bottom = 300.0
grow_horizontal = 0
grow_vertical = 2
texture = ExtResource("3_panel")
expand_mode = 1
stretch_mode = 5

[node name="ButtonContainer" type="VBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 40.0
offset_top = 80.0
offset_right = -40.0
offset_bottom = -80.0
grow_horizontal = 2
grow_vertical = 2

[node name="NovaHraButton" type="TextureButton" parent="MainPanel/ButtonContainer"]
custom_minimum_size = Vector2(320, 128)
layout_mode = 2
texture_normal = ExtResource("6_button_nova")
stretch_mode = 5

[node name="NovaHraLabel" type="Label" parent="MainPanel/ButtonContainer/NovaHraButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.831, 0.686, 0.216, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_constants/outline_size = 1
theme_override_fonts/font = ExtResource("11_font")
theme_override_font_sizes/font_size = 18
text = "NOVÁ HRA"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer1" type="Control" parent="MainPanel/ButtonContainer"]
custom_minimum_size = Vector2(0, 15)
layout_mode = 2

[node name="KapitolyButton" type="TextureButton" parent="MainPanel/ButtonContainer"]
custom_minimum_size = Vector2(320, 128)
layout_mode = 2
texture_normal = ExtResource("7_button_kapitoly")
stretch_mode = 5

[node name="KapitolyLabel" type="Label" parent="MainPanel/ButtonContainer/KapitolyButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.831, 0.686, 0.216, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_constants/outline_size = 1
theme_override_fonts/font = ExtResource("11_font")
theme_override_font_sizes/font_size = 18
text = "KAPITOLY"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer2" type="Control" parent="MainPanel/ButtonContainer"]
custom_minimum_size = Vector2(0, 15)
layout_mode = 2

[node name="NastaveniaButton" type="TextureButton" parent="MainPanel/ButtonContainer"]
custom_minimum_size = Vector2(320, 128)
layout_mode = 2
texture_normal = ExtResource("8_button_nastavenia")
stretch_mode = 5

[node name="NastaveniaLabel" type="Label" parent="MainPanel/ButtonContainer/NastaveniaButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.831, 0.686, 0.216, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_constants/outline_size = 1
theme_override_fonts/font = ExtResource("11_font")
theme_override_font_sizes/font_size = 18
text = "NASTAVENIA"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer3" type="Control" parent="MainPanel/ButtonContainer"]
custom_minimum_size = Vector2(0, 15)
layout_mode = 2

[node name="OHreButton" type="TextureButton" parent="MainPanel/ButtonContainer"]
custom_minimum_size = Vector2(320, 128)
layout_mode = 2
texture_normal = ExtResource("9_button_o_hre")
stretch_mode = 5

[node name="OHreLabel" type="Label" parent="MainPanel/ButtonContainer/OHreButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.831, 0.686, 0.216, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_constants/outline_size = 1
theme_override_fonts/font = ExtResource("11_font")
theme_override_font_sizes/font_size = 18
text = "O HRE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TopDecoration" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -300.0
offset_top = 20.0
offset_right = 300.0
offset_bottom = 148.0
grow_horizontal = 2
texture = ExtResource("10_decoration")
expand_mode = 1
stretch_mode = 5

[node name="BottomDecoration" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -300.0
offset_top = -148.0
offset_right = 300.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource("10_decoration")
expand_mode = 1
stretch_mode = 5



[node name="ProgressBar" type="ProgressBar" parent="."]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
show_percentage = false
