extends Node

# Singleton pre riadenie hry
signal chapter_completed(chapter_number: int)
signal puzzle_completed(chapter_number: int, puzzle_number: int)

# Stav hry
var current_chapter: int = 1
var story_phase: int = 0  # Aktuálna fáza príbehu v kapitole
var completed_chapters: Array[int] = []
var completed_puzzles: Dictionary = {} # {chapter_number: [puzzle1, puzzle2]}
var last_played_chapter: int = 1
var last_story_phase: int = 0
var last_dialogue_index: int = 0
var current_game_state: String = "story"  # "story", "dialogue", "puzzle"
var current_puzzle_name: String = ""
var current_dialogue_data: Array[Dictionary] = []
var is_continuing_game: bool = false  # Flag pre rozlíšenie nová hra vs pokračovanie
var game_settings: Dictionary = {
	"master_volume": 1.0,
	"music_volume": 1.0,
	"sfx_volume": 1.0
}

# Informácie o kapitolách
var chapter_info: Dictionary = {
	1: {
		"title": "Kapitola 1: <PERSON><PERSON><PERSON><PERSON><PERSON> začiatok",
		"description": "Marec 1894. Cesta k zámku Van Helsinga cez karpatské horstvo. Rozlúštite Van Helsingove šifry a nájdite cestu k zámku.",
		"puzzles": ["Van Helsingova šifra", "Cesta lesom"]
	},
	2: {
		"title": "Kapitola 2: Hlbšie do temnoty",
		"description": "Brána zámku. Dokážte, že patríte k Rádu a vstúpte do Van Helsingovho sídla.",
		"puzzles": ["Krvavý nápis", "Skúška Rádu"]
	},
	3: {
		"title": "Kapitola 3: Pátranie v zámku",
		"description": "Vstúpte do Van Helsingovho zámku a nájdite stopy po jeho zmiznutí.",
		"puzzles": ["Obrátená správa", "Jednoduchý výpočet"]
	},
	4: {
		"title": "Kapitola 4: Tajné krídlo",
		"description": "Staré krídlo zámku plné pascí a alchymistických tajomstiev.",
		"puzzles": ["Pamäťový test", "Vampírska aritmetika"]
	},
	5: {
		"title": "Kapitola 5: Krypty",
		"description": "Zostup do pradávnych krypt plných tajomstiev a nebezpečenstiev.",
		"puzzles": ["Kód z tieňov", "Tri páky"]
	},
	6: {
		"title": "Kapitola 6: Konfrontácia",
		"description": "Finálny súboj s grófkou Isabelle Báthoryovou. Osud sveta je vo vašich rukách.",
		"puzzles": ["Tri sestry", "Rytmus rituálu"]
	},
	7: {
		"title": "Epilóg: Záchrana mentora",
		"description": "Záchrana doktora Van Helsinga a návrat do Budapešti. Prekliate dedičstvo je definitívne zlomené.",
		"puzzles": []
	}
}

func _ready():
	load_game_data()

# Navigácia medzi scénami
func go_to_main_menu():
	print("🎨 Prechod do hlavného menu")
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func go_to_chapters():
	print("📚 Prechod do chapters menu")
	var scene_paths = [
		"res://scenes/EnhancedChaptersMenu.tscn",
		"res://scenes/NewChaptersMenu.tscn",
		"res://SimpleChaptersMenu.tscn"
	]

	for scene_path in scene_paths:
		print("🔍 Kontrolujem existenciu scény: ", scene_path)
		if ResourceLoader.exists(scene_path):
			print("✅ Scéna existuje, načítavam...")
			get_tree().change_scene_to_file(scene_path)
			return
		else:
			print("❌ Scéna neexistuje: ", scene_path)

	print("❌ Žiadna chapters scéna nie je dostupná!")
	print("🔄 Zostávam v aktuálnej scéne")

func go_to_chapter(chapter_number: int):
	current_chapter = chapter_number
	last_played_chapter = chapter_number
	last_story_phase = 0  # Reset story phase when starting new chapter
	is_continuing_game = false  # Nová kapitola, nie pokračovanie
	save_game_data()  # Save progress immediately

	# Mobile debugging
	var platform = OS.get_name()
	if platform == "iOS" or platform == "Android":
		print("📱 ", platform, ": GameManager.go_to_chapter(", chapter_number, ")")

	# Zastaviť všetky audio efekty z predchádzajúcich kapitol
	if AudioManager:
		AudioManager.stop_all_chapter_audio()

	print("🎬 Spúšťam intro pre kapitolu ", chapter_number)

	# Kontrola existencie ChapterIntro scény
	var intro_scene = "res://scenes/ChapterIntro.tscn"
	if ResourceLoader.exists(intro_scene):
		print("✅ ChapterIntro scéna existuje, načítavam...")
		get_tree().change_scene_to_file(intro_scene)
	else:
		print("❌ ChapterIntro scéna neexistuje! Fallback na priame načítanie kapitoly")
		# Fallback - načítaj kapitolu priamo
		var chapter_scene = "res://scenes/Chapter" + str(chapter_number) + ".tscn"
		if ResourceLoader.exists(chapter_scene):
			get_tree().change_scene_to_file(chapter_scene)
		else:
			print("❌ Ani kapitola scéna neexistuje!")

func go_to_settings():
	print("⚙️ Prechod do nastavení")
	get_tree().change_scene_to_file("res://scenes/SettingsMenuNew.tscn")

func go_to_about():
	print("ℹ️ Prechod do o hre")
	get_tree().change_scene_to_file("res://scenes/AboutGameNew.tscn")

# Správa progresu
func complete_puzzle(chapter_number: int, puzzle_number: int):
	if not completed_puzzles.has(chapter_number):
		completed_puzzles[chapter_number] = []

	if puzzle_number not in completed_puzzles[chapter_number]:
		completed_puzzles[chapter_number].append(puzzle_number)
		puzzle_completed.emit(chapter_number, puzzle_number)

	# Ak sú dokončené oba hlavolamy, kapitola je hotová
	# Kapitoly 1-6 majú hlavolamy, kapitola 7 (epilóg) sa dokončuje manuálne
	if chapter_number < 7 and completed_puzzles[chapter_number].size() >= 2:
		complete_chapter(chapter_number)

# Špeciálna funkcia pre dokončenie epilógu
func complete_epilogue():
	complete_chapter(7)

func complete_chapter(chapter_number: int):
	if chapter_number not in completed_chapters:
		completed_chapters.append(chapter_number)
		chapter_completed.emit(chapter_number)
	save_game_data()

func is_chapter_unlocked(chapter_number: int) -> bool:
	if chapter_number == 1:
		return true
	# Pre testovanie - odomknúť kapitoly 2, 3, 4, 5, 6 a 7
	if chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6 or chapter_number == 7:
		return true
	return (chapter_number - 1) in completed_chapters

func is_puzzle_completed(chapter_number: int, puzzle_number: int) -> bool:
	if not completed_puzzles.has(chapter_number):
		return false
	return puzzle_number in completed_puzzles[chapter_number]

# Ukladanie a načítavanie
func save_game_data():
	var save_data = {
		"completed_chapters": completed_chapters,
		"completed_puzzles": completed_puzzles,
		"last_played_chapter": last_played_chapter,
		"last_story_phase": last_story_phase,
		"last_dialogue_index": last_dialogue_index,
		"current_game_state": current_game_state,
		"current_puzzle_name": current_puzzle_name,
		"current_dialogue_data": current_dialogue_data,
		"game_settings": game_settings
	}

	var save_file = FileAccess.open("user://savegame.save", FileAccess.WRITE)
	if save_file:
		save_file.store_string(JSON.stringify(save_data))
		save_file.close()
		print("✅ Hra uložená")
	else:
		print("❌ Chyba pri ukladaní hry")

func load_game_data():
	var save_file = FileAccess.open("user://savegame.save", FileAccess.READ)
	if save_file:
		var json_string = save_file.get_as_text()
		save_file.close()

		var json = JSON.new()
		var parse_result = json.parse(json_string)

		if parse_result == OK:
			var save_data = json.data
			var loaded_chapters = save_data.get("completed_chapters", [])
			# Konvertovať Array na Array[int] pre type safety
			completed_chapters.clear()
			for chapter in loaded_chapters:
				if chapter is int:
					completed_chapters.append(chapter)
			completed_puzzles = save_data.get("completed_puzzles", {})
			last_played_chapter = save_data.get("last_played_chapter", 1)
			last_story_phase = save_data.get("last_story_phase", 0)
			last_dialogue_index = save_data.get("last_dialogue_index", 0)
			current_game_state = save_data.get("current_game_state", "story")
			current_puzzle_name = save_data.get("current_puzzle_name", "")
			var loaded_dialogue_data = save_data.get("current_dialogue_data", [])
			# Konvertovať Array na Array[Dictionary] pre type safety
			current_dialogue_data.clear()
			for dialogue_item in loaded_dialogue_data:
				if dialogue_item is Dictionary:
					current_dialogue_data.append(dialogue_item)
			game_settings = save_data.get("game_settings", game_settings)

# Nastavenia
func update_setting(setting_name: String, value):
	game_settings[setting_name] = value
	apply_settings()
	save_game_data()

func apply_settings():
	# Aplikovanie nastavení
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Master"),
		linear_to_db(game_settings.master_volume))

# Systém pokračovania hry
func has_save_game() -> bool:
	return FileAccess.file_exists("user://savegame.save")

func get_continue_chapter() -> int:
	if not has_save_game():
		return 1

	# Ak je posledná hraná kapitola dokončená, pokračuj na ďalšiu
	if last_played_chapter in completed_chapters:
		var next_chapter = last_played_chapter + 1
		if next_chapter <= 7:  # Máme 7 kapitol
			return next_chapter
		else:
			return 7  # Zostať na epilógu

	# Inak pokračuj na poslednej hranej kapitole
	return last_played_chapter

func continue_game():
	var continue_chapter = get_continue_chapter()
	# Nastaviť flag že pokračujeme v hre (nie nová hra)
	is_continuing_game = true
	current_chapter = continue_chapter
	last_played_chapter = continue_chapter
	print("🎮 Pokračujem v hre - kapitola: ", continue_chapter, ", stav: ", current_game_state)
	print("🎮 Nastavujem current_chapter a last_played_chapter na: ", continue_chapter)

	# Mobile debugging
	var platform = OS.get_name()
	if platform == "iOS" or platform == "Android":
		print("📱 ", platform, ": continue_game() - kapitola ", continue_chapter)

	# Okamžite zastav main menu hudbu a všetky audio efekty pred načítaním kapitoly
	if AudioManager:
		AudioManager.stop_main_menu_music_immediately()
		AudioManager.stop_all_chapter_audio()

	# Mobilné kompatibilné načítanie scény
	var chapter_scene = "res://scenes/Chapter" + str(continue_chapter) + ".tscn"
	if ResourceLoader.exists(chapter_scene):
		print("✅ Načítavam kapitolu: ", chapter_scene)
		get_tree().change_scene_to_file(chapter_scene)
	else:
		print("❌ Kapitola scéna neexistuje: ", chapter_scene)
		# Fallback na chapters menu
		go_to_chapters()

func update_story_progress(chapter: int, story_phase: int, dialogue_index: int = 0):
	last_played_chapter = chapter
	last_story_phase = story_phase
	last_dialogue_index = dialogue_index
	save_game_data()

# Nové funkcie pre tracking presného stavu
func set_game_state_story():
	"""Nastaví stav hry na story mode"""
	current_game_state = "story"
	current_puzzle_name = ""
	current_dialogue_data = []
	save_game_data()

func set_game_state_dialogue(dialogue_data: Array[Dictionary], dialogue_index: int = 0):
	"""Nastaví stav hry na dialogue mode s konkrétnymi dátami"""
	current_game_state = "dialogue"
	current_dialogue_data = dialogue_data
	last_dialogue_index = dialogue_index
	current_puzzle_name = ""
	save_game_data()

func set_game_state_puzzle(puzzle_name: String):
	"""Nastaví stav hry na puzzle mode"""
	current_game_state = "puzzle"
	current_puzzle_name = puzzle_name
	current_dialogue_data = []
	save_game_data()

func restore_exact_game_state():
	"""Obnoví presný stav hry po načítaní kapitoly"""
	print("🔄 Obnovujem stav hry: ", current_game_state)

	# Počkať na načítanie scény
	await get_tree().process_frame
	await get_tree().process_frame

	var current_scene = get_tree().current_scene
	if not current_scene:
		print("❌ Chyba: Žiadna aktuálna scéna")
		return

	match current_game_state:
		"dialogue":
			print("💬 Obnovujem dialóg na pozícii: ", last_dialogue_index)
			restore_dialogue_state(current_scene)
		"puzzle":
			print("🧩 Obnovujem puzzle: ", current_puzzle_name)
			restore_puzzle_state(current_scene)
		"story":
			print("📖 Obnovujem story mode")
			restore_story_state(current_scene)

func restore_dialogue_state(scene):
	"""Obnoví dialóg na správnej pozícii"""
	var dialogue_system = scene.get_node("DialogueSystem")
	if dialogue_system and current_dialogue_data.size() > 0:
		# Nastaviť kapitolu pre správne audio
		dialogue_system.set_current_chapter(last_played_chapter)
		dialogue_system.restore_dialogue_position(current_dialogue_data, last_dialogue_index)

func restore_puzzle_state(scene):
	"""Obnoví puzzle stav"""
	# Nájsť a otvoriť správny puzzle
	if current_puzzle_name != "":
		var puzzle_scene = load("res://scenes/" + current_puzzle_name + ".tscn")
		if puzzle_scene:
			var puzzle_instance = puzzle_scene.instantiate()
			scene.add_child(puzzle_instance)

func restore_story_state(scene):
	"""Obnoví story stav na správnej fáze"""
	# Nastaviť správnu story_phase
	if scene.has_method("set_story_phase"):
		scene.set_story_phase(last_story_phase)

# Enhanced UI funkcie pre nové menu



func get_chapter_puzzles_completed(chapter_id: int) -> int:
	if not completed_puzzles.has(chapter_id):
		return 0
	var chapter_completed_puzzles = completed_puzzles[chapter_id]
	return chapter_completed_puzzles.size()

func has_progress_data() -> bool:
	return not completed_chapters.is_empty() or not completed_puzzles.is_empty()

func reset_all_progress():
	completed_chapters.clear()
	completed_puzzles.clear()
	current_chapter = 1
	save_game_data()
	print("🔄 Celý progress resetovaný")

func load_chapter(chapter_id: int):
	print("📖 Načítavam kapitolu %d" % chapter_id)
	go_to_chapter(chapter_id)
