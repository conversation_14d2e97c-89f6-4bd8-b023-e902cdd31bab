extends Control

func _ready():
	print("🧪 Test načítania chapters menu")
	
	# Test existencie scén
	var scenes_to_test = [
		"res://scenes/EnhancedChaptersMenu.tscn",
		"res://scenes/NewChaptersMenu.tscn",
		"res://scenes/ChaptersMenuTest.tscn"
	]
	
	for scene_path in scenes_to_test:
		if ResourceLoader.exists(scene_path):
			print("✅ Scéna existuje: ", scene_path)
		else:
			print("❌ Scéna neexistuje: ", scene_path)
	
	# Test načítania Enhanced Chapters Menu
	print("🔄 Pokúšam sa načítať EnhancedChaptersMenu...")
	var scene_resource = load("res://scenes/EnhancedChaptersMenu.tscn")
	if scene_resource:
		print("✅ Scéna sa načítala úspešne")
		var scene_instance = scene_resource.instantiate()
		if scene_instance:
			print("✅ Inštancia sa vytvorila úspešne")
			add_child(scene_instance)
			print("✅ Scéna sa pridala do stromu")
		else:
			print("❌ Nepodarilo sa vytvoriť inštanciu")
	else:
		print("❌ Nepodarilo sa načítať scénu")

	# Test GameManager.go_to_chapters()
	print("🔄 Testujem GameManager.go_to_chapters()...")
	await get_tree().create_timer(2.0).timeout
	print("🔄 Volám GameManager.go_to_chapters()...")
	GameManager.go_to_chapters()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")
