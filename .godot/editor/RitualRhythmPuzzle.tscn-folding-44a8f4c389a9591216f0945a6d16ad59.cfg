[folding]

node_unfolds=[NodePath("."), PackedStringArray("Layout", "Theme"), NodePath("Background"), PackedStringArray("Layout"), NodePath("PuzzlePanel"), PackedStringArray("Layout", "Patch Margin"), NodePath("PuzzlePanel/VBoxContainer"), PackedStringArray("Layout", "Theme Overrides"), NodePath("PuzzlePanel/VBoxContainer/TitleLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer1"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/DescriptionLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("PuzzlePanel/VBoxContainer/Spacer2"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/RitualLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer3"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/SymbolsContainer"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/SymbolsContainer/CrossButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/SymbolsContainer/CrossButton/CrossLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/SymbolsContainer/WaterButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/SymbolsContainer/WaterButton/WaterLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/SymbolsContainer/FireButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/SymbolsContainer/FireButton/FireLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/SymbolsContainer/SaltButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/SymbolsContainer/SaltButton/SaltLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer4"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/SequenceLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer5"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/HintButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/HintButton/HintLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton/ResetLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton/CloseLabel"), PackedStringArray("Layout")]
resource_unfolds=["res://scenes/RitualRhythmPuzzle.tscn::LabelSettings_title", PackedStringArray("Resource", "Font", "Outline", "Shadow"), "res://scenes/RitualRhythmPuzzle.tscn::LabelSettings_ritual", PackedStringArray("Resource", "Font", "Outline", "Shadow"), "res://scenes/RitualRhythmPuzzle.tscn::LabelSettings_sequence", PackedStringArray("Resource", "Font", "Outline", "Shadow")]
nodes_folded=[]
