[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Layout"), NodePath("Background"), PackedStringArray("Layout"), NodePath("MainContainer"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/TopSpacer"), PackedStringArray("Layout"), NodePath("MainContainer/VBoxContainer/TitleSection"), PackedStringArray("Layout"), NodePath("MainContainer/VBoxContainer/TitleSection/TitleVBox"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/TitleSection/TitleVBox/TitleLabel"), PackedStringArray("Layout", "Theme Overrides"), Node<PERSON>ath("MainContainer/VBoxContainer/TitleSection/TitleVBox/SubtitleLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/MiddleSpacer"), PackedStringArray("Layout"), NodePath("MainContainer/VBoxContainer/MenuSection"), PackedStringArray("Layout"), NodePath("MainContainer/VBoxContainer/MenuSection/MenuVBox"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer"), PackedStringArray("Layout"), NodePath("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/NovaHraButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/PokracovatButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/KapitolyButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/NastaveniaButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/OHreButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("MainContainer/VBoxContainer/BottomSpacer"), PackedStringArray("Layout"), NodePath("MainContainer/VBoxContainer/VersionLabel"), PackedStringArray("Layout", "Theme Overrides")]
resource_unfolds=["res://scenes/MainMenu.tscn::StyleBoxTexture_button_normal", PackedStringArray("Resource", "Texture Margins")]
nodes_folded=[]
