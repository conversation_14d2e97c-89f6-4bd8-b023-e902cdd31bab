[res://autoload/GameManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 264,
"scroll_position": 264.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/EnhancedMainMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 168,
"scroll_position": 143.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/SplashScreen.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 161,
"scroll_position": 133.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/EnhancedChaptersMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 219,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/SettingsMenuNew.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 22,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
