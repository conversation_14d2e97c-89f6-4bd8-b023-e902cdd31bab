extends Control

# Mobile-first UI references
@onready var main_container = $MainContainer
@onready var background = $Background
@onready var title_label = $TitleLabel

# Button references - new mobile-optimized structure
@onready var nova_hra_button = $MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/NovaHraButton
@onready var pokracovat_button = $MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/PokracovatButton
@onready var kapitoly_button = $MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/KapitolyButton
@onready var nastavenia_button = $MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/NastaveniaButton
@onready var o_hre_button = $MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/OHreButton

func _ready():
	setup_mobile_ui()
	setup_background()
	setup_buttons()
	setup_audio()
	adapt_to_screen_size()
	update_continue_button_visibility()

func setup_mobile_ui():
	"""Initialize mobile-first UI elements"""
	title_label.text = "Prekliate dedičstvo"

func setup_background():
	"""Setup responsive background"""
	background.texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png")
	background.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED

func setup_buttons():
	"""Connect button signals for mobile-optimized buttons"""
	# Connect button signals
	nova_hra_button.pressed.connect(_on_nova_hra_pressed)
	pokracovat_button.pressed.connect(_on_pokracovat_pressed)
	kapitoly_button.pressed.connect(_on_kapitoly_pressed)
	nastavenia_button.pressed.connect(_on_nastavenia_pressed)
	o_hre_button.pressed.connect(_on_o_hre_pressed)

	# Setup button hover effects for better mobile UX
	setup_button_hover_effects()

	# Pripoj signály
	nova_hra_button.pressed.connect(_on_nova_hra_pressed)
	pokracovat_button.pressed.connect(_on_pokracovat_pressed)
	kapitoly_button.pressed.connect(_on_kapitoly_pressed)
	nastavenia_button.pressed.connect(_on_nastavenia_pressed)
	o_hre_button.pressed.connect(_on_o_hre_pressed)

	# Skryť/zobraziť pokračovať button podľa toho, či existuje save
	update_continue_button_visibility()

	# Nastaviť fokus na prvé dostupné tlačidlo
	if pokracovat_button.visible:
		pokracovat_button.grab_focus()
	else:
		nova_hra_button.grab_focus()

func setup_audio():
	# Okamžite zastav akúkoľvek hudbu a všetky herné zvuky, potom spusti main menu hudbu
	if AudioManager:
		# Zastav akúkoľvek hudbu bez fade-u
		AudioManager.stop_music(false)
		# Zastav všetky herné zvuky (atmosféra, efekty, atď.)
		AudioManager.stop_all_chapter_audio()
		# Spusti main menu hudbu
		AudioManager.play_music("main_menu")

func adapt_to_screen_size():
	"""Mobile-first responsive design adaptation"""
	var viewport = get_viewport()
	if not viewport or not is_instance_valid(viewport):
		print("⚠️ MainMenu: Viewport nie je dostupný pre adaptáciu veľkosti")
		return

	var screen_size = viewport.get_visible_rect().size
	var screen_width = screen_size.x
	var screen_height = screen_size.y
	var aspect_ratio = screen_width / screen_height

	print("📱 MainMenu: Adaptácia na rozlíšenie ", screen_size, " (pomer: ", aspect_ratio, ")")

	# Mobile-first approach: optimize for portrait screens
	if screen_width <= 480:  # Small mobile phones
		adapt_for_small_mobile()
	elif screen_width <= 768:  # Large phones / small tablets
		adapt_for_large_mobile()
	elif screen_width <= 1024:  # Tablets
		adapt_for_tablet()
	else:  # Desktop
		adapt_for_desktop()

	# Ensure buttons are properly sized for mobile (80% of container width)
	adapt_button_sizes(screen_width)

func adapt_for_small_mobile():
	"""Optimize for small mobile screens (≤480px)"""
	main_container.add_theme_constant_override("margin_left", 20)
	main_container.add_theme_constant_override("margin_right", 20)
	main_container.add_theme_constant_override("margin_top", 40)
	main_container.add_theme_constant_override("margin_bottom", 40)

	# Enhanced typography - visually dominant title (2x button size)
	title_label.add_theme_font_size_override("font_size", 40)  # 2x button size (20px)

	# Button text for mobile (base size)
	for button in get_buttons():
		button.add_theme_font_size_override("font_size", 20)

func adapt_for_large_mobile():
	"""Optimize for large mobile screens (481-768px)"""
	main_container.add_theme_constant_override("margin_left", 30)
	main_container.add_theme_constant_override("margin_right", 30)
	main_container.add_theme_constant_override("margin_top", 50)
	main_container.add_theme_constant_override("margin_bottom", 50)

	# Enhanced typography - visually dominant title (1.8x button size)
	title_label.add_theme_font_size_override("font_size", 44)  # 1.8x button size (24px)

	for button in get_buttons():
		button.add_theme_font_size_override("font_size", 24)

func adapt_for_tablet():
	"""Optimize for tablet screens (769-1024px)"""
	main_container.add_theme_constant_override("margin_left", 60)
	main_container.add_theme_constant_override("margin_right", 60)
	main_container.add_theme_constant_override("margin_top", 60)
	main_container.add_theme_constant_override("margin_bottom", 60)

	# Enhanced typography - visually dominant title (1.7x button size)
	title_label.add_theme_font_size_override("font_size", 52)  # 1.7x button size (30px)

	for button in get_buttons():
		button.add_theme_font_size_override("font_size", 30)

func adapt_for_desktop():
	"""Optimize for desktop screens (>1024px)"""
	main_container.add_theme_constant_override("margin_left", 80)
	main_container.add_theme_constant_override("margin_right", 80)
	main_container.add_theme_constant_override("margin_top", 80)
	main_container.add_theme_constant_override("margin_bottom", 80)

	# Enhanced typography - visually dominant title (1.6x button size)
	title_label.add_theme_font_size_override("font_size", 58)  # 1.6x button size (36px)

	for button in get_buttons():
		button.add_theme_font_size_override("font_size", 36)

func adapt_button_sizes(screen_width: float):
	"""Ensure buttons are 80% of screen width for mobile"""
	var button_width = min(screen_width * 0.8, 400)  # Max 400px for desktop
	var button_height = 60

	if screen_width <= 480:
		button_height = 55  # Slightly smaller for small screens
	elif screen_width > 1024:
		button_height = 70  # Larger for desktop

	for button in get_buttons():
		button.custom_minimum_size = Vector2(button_width, button_height)

func get_buttons() -> Array:
	"""Get all menu buttons"""
	return [nova_hra_button, pokracovat_button, kapitoly_button, nastavenia_button, o_hre_button]

func setup_button_hover_effects():
	"""Setup hover effects for better mobile UX"""
	for button in get_buttons():
		button.mouse_entered.connect(_on_button_hover.bind(button))
		button.mouse_exited.connect(_on_button_unhover.bind(button))

func _on_button_hover(button: Button):
	"""Button hover effect"""
	var tween = create_tween()
	tween.tween_property(button, "scale", Vector2(1.05, 1.05), 0.1)

func _on_button_unhover(button: Button):
	"""Button unhover effect"""
	var tween = create_tween()
	tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.1)

# Button callbacks
func _on_nova_hra_pressed():
	print("Starting new game...")
	AudioManager.play_menu_button_sound()
	start_new_game_with_fade()

func _on_pokracovat_pressed():
	print("Continuing game...")
	AudioManager.play_menu_button_sound()
	GameManager.continue_game()

func _on_kapitoly_pressed():
	print("📚 Kapitoly button pressed")

	# Mobile debugging
	var platform = OS.get_name()
	if platform == "iOS" or platform == "Android":
		print("📱 ", platform, ": Spúšťam kapitoly menu")

	# Prehrať zvuk
	if AudioManager:
		AudioManager.play_menu_button_sound()

	# Mobilné kompatibilné načítanie scény - používať ResourceLoader pre všetky platformy
	var scene_path = "res://scenes/NewChaptersMenu.tscn"

	# Používať ResourceLoader.exists() pre všetky platformy (lepšie pre mobile)
	if ResourceLoader.exists(scene_path):
		print("✅ Načítavam scénu: ", scene_path)
		get_tree().change_scene_to_file(scene_path)
	else:
		print("❌ Scéna neexistuje: ", scene_path)
		print("🔄 Pokúšam sa načítať alternatívnu scénu...")
		# Fallback na test scénu
		var fallback_scene = "res://scenes/ChaptersMenuTest.tscn"
		if ResourceLoader.exists(fallback_scene):
			print("✅ Načítavam fallback scénu: ", fallback_scene)
			get_tree().change_scene_to_file(fallback_scene)
		else:
			print("❌ Žiadna chapters scéna nie je dostupná!")
			# Posledný fallback - zostať v main menu
			print("🔄 Zostávam v hlavnom menu")

func _on_nastavenia_pressed():
	print("⚙️ Nastavenia button pressed")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/SettingsMenuNew.tscn")

func _on_o_hre_pressed():
	print("📖 O Hre button pressed")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/AboutGameNew.tscn")

func start_new_game_with_fade():
	"""Spustí novú hru s fade efektom a zvukom začiatku"""
	# Vytvor fade overlay
	var fade_overlay = ColorRect.new()
	fade_overlay.color = Color.BLACK
	fade_overlay.color.a = 0.0
	fade_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(fade_overlay)

	# Fade to black
	var tween = create_tween()
	tween.tween_property(fade_overlay, "color:a", 1.0, 1.0)
	await tween.finished

	# Prehrať zvuk začiatku počas fade
	AudioManager.play_game_start_sound()

	# Krátka pauza pre zvuk
	await get_tree().create_timer(0.5).timeout

	# Spustiť novú hru cez GameManager (s intro video)
	GameManager.go_to_chapter(1)

func update_continue_button_visibility():
	"""Aktualizuje viditeľnosť pokračovať buttonu podľa existencie save súboru"""
	if pokracovat_button:
		pokracovat_button.visible = GameManager.has_save_game()
		print("Continue button visibility: ", pokracovat_button.visible)
