extends Control

# Enhanced Main Menu s novými UI assetmi
# Používa len nové exportované assety z PSD súborov

# Tlačidlá - budú inicializované v _ready()
var nova_hra_button: Button
var pokracovat_button: Button
var kapitoly_button: Button
var nastavenia_button: Button
var o_hre_button: Button
var animation_player: AnimationPlayer

# Progress tracking pre vizuálny feedback
var chapter_progress: Dictionary = {}

func _ready():
	print("🎨 Enhanced Main Menu načítané s novými UI assetmi")
	# Najprv inicializovať uzly
	initialize_nodes()
	# Potom použiť call_deferred pre bezpečnejšie volanie funkcií
	call_deferred("setup_menu")
	call_deferred("setup_audio")
	call_deferred("setup_animations")
	call_deferred("update_continue_button_visibility")
	call_deferred("adapt_to_screen_size")

func initialize_nodes():
	"""Inicializácia uzlov s kontrolou chýb"""
	print("🔧 Inicializujem uzly...")

	nova_hra_button = get_node_or_null("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/NovaHraButton")
	if not nova_hra_button:
		print("❌ nova_hra_button sa nepodarilo načítať")
		return

	pokracovat_button = get_node_or_null("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/PokracovatButton")
	if not pokracovat_button:
		print("❌ pokracovat_button sa nepodarilo načítať")
		return

	kapitoly_button = get_node_or_null("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/KapitolyButton")
	if not kapitoly_button:
		print("❌ kapitoly_button sa nepodarilo načítať")
		return

	nastavenia_button = get_node_or_null("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/NastaveniaButton")
	if not nastavenia_button:
		print("❌ nastavenia_button sa nepodarilo načítať")
		return

	o_hre_button = get_node_or_null("MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer/OHreButton")
	if not o_hre_button:
		print("❌ o_hre_button sa nepodarilo načítať")
		return

	animation_player = get_node_or_null("AnimationPlayer")
	if not animation_player:
		print("❌ animation_player sa nepodarilo načítať")

	print("✅ Všetky uzly úspešne inicializované")

func setup_menu():
	"""Nastavenie menu s novými assetmi"""
	print("🔧 Nastavujem menu...")

	# Kontrola, či sú tlačidlá dostupné
	if not nova_hra_button:
		print("❌ nova_hra_button nie je dostupný")
		return

	print("✅ Pripájam signály...")
	# Pripojenie signálov
	nova_hra_button.pressed.connect(_on_nova_hra_pressed)
	pokracovat_button.pressed.connect(_on_pokracovat_pressed)
	kapitoly_button.pressed.connect(_on_kapitoly_pressed)
	nastavenia_button.pressed.connect(_on_nastavenia_pressed)
	o_hre_button.pressed.connect(_on_o_hre_pressed)

	print("✅ Nastavujem hover efekty...")
	# Hover efekty pre lepší UX
	setup_button_effects(nova_hra_button)
	setup_button_effects(pokracovat_button)
	setup_button_effects(kapitoly_button)
	setup_button_effects(nastavenia_button)
	setup_button_effects(o_hre_button)

	print("✅ Nastavujem fokus...")
	# Nastavenie fokusu
	if pokracovat_button and pokracovat_button.visible:
		pokracovat_button.grab_focus()
	else:
		nova_hra_button.grab_focus()

	print("✅ Menu nastavené")

func setup_audio():
	"""Nastavenie audio pre Enhanced Main Menu"""
	if AudioManager:
		# Zastav akúkoľvek hudbu a herné zvuky
		AudioManager.stop_music(false)
		AudioManager.stop_all_chapter_audio()
		# Spusti main menu hudbu
		AudioManager.play_music("main_menu")
		print("🎵 Enhanced Main Menu: Audio nastavené")

func setup_button_effects(button: Button):
	"""Pridanie hover efektov pre tlačidlá"""
	button.mouse_entered.connect(_on_button_hover.bind(button))
	button.mouse_exited.connect(_on_button_unhover.bind(button))

func _on_button_hover(button: Button):
	"""Hover efekt pre tlačidlá"""
	var tween = create_tween()
	tween.tween_property(button, "scale", Vector2(1.05, 1.05), 0.1)
	if AudioManager:
		AudioManager.play_menu_hover_sound()

func _on_button_unhover(button: Button):
	"""Unhover efekt pre tlačidlá"""
	var tween = create_tween()
	tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.1)

func setup_animations():
	"""Nastavenie animácií pre menu"""
	# Fade in animácia pri štarte - použiť Tween namiesto AnimationPlayer
	modulate = Color(1, 1, 1, 0)
	call_deferred("_start_fade_in_animation")

func _start_fade_in_animation():
	"""Spustí fade in animáciu pomocou Tween"""
	print("🎨 Spúšťam fade in animáciu pre Enhanced Main Menu")
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color(1, 1, 1, 1), 1.0)
	tween.finished.connect(func(): print("✅ Fade in animácia dokončená"))

func update_continue_button_visibility():
	"""Aktualizácia viditeľnosti pokračovať tlačidla"""
	if not pokracovat_button or not is_instance_valid(pokracovat_button):
		print("⚠️ pokracovat_button nie je dostupný")
		return

	var has_save = GameManager.has_save_game()
	pokracovat_button.visible = has_save

	# Aktualizácia textu podľa progresu
	if has_save:
		var continue_chapter = GameManager.get_continue_chapter()
		var completed_count = GameManager.completed_chapters.size()
		pokracovat_button.text = "▶️ Pokračovať (Kapitola %d)" % continue_chapter

		# Pridanie progress info
		if completed_count > 0:
			pokracovat_button.text += " - %d/7 dokončených" % completed_count

func adapt_to_screen_size():
	"""Adaptácia na veľkosť obrazovky"""
	var screen_size = get_viewport().get_visible_rect().size
	var is_mobile = screen_size.x < screen_size.y
	
	# Získaj referenciu na dominantný nadpis
	var dominant_title = $MainContainer/VBoxContainer/DominantTitle

	if is_mobile:
		# Mobilná optimalizácia
		$MainContainer.add_theme_constant_override("margin_left", 20)
		$MainContainer.add_theme_constant_override("margin_right", 20)
		$MainContainer.add_theme_constant_override("margin_top", 20)
		$MainContainer.add_theme_constant_override("margin_bottom", 20)

		# Menší font pre mobil
		if dominant_title:
			dominant_title.add_theme_font_size_override("font_size", 56)
	else:
		# Desktop optimalizácia
		$MainContainer.add_theme_constant_override("margin_left", 60)
		$MainContainer.add_theme_constant_override("margin_right", 60)
		$MainContainer.add_theme_constant_override("margin_top", 40)
		$MainContainer.add_theme_constant_override("margin_bottom", 40)

		# Desktop font už je nastavený v scéne (84px)

# Button callbacks s vylepšenými efektmi
func _on_nova_hra_pressed():
	print("🆕 Nová hra - Enhanced UI")
	play_button_sound()
	start_new_game_with_enhanced_transition()

func _on_pokracovat_pressed():
	print("▶️ Pokračovať - Enhanced UI")
	play_button_sound()
	continue_game_with_enhanced_transition()

func _on_kapitoly_pressed():
	print("📚 Kapitoly - Enhanced UI")
	play_button_sound()
	go_to_chapters_with_transition()

func _on_nastavenia_pressed():
	print("⚙️ Nastavenia - Enhanced UI")
	play_button_sound()
	go_to_settings_with_transition()

func _on_o_hre_pressed():
	print("ℹ️ O hre - Enhanced UI")
	play_button_sound()
	go_to_about_with_transition()

func play_button_sound():
	"""Prehranie zvuku tlačidla"""
	if AudioManager:
		AudioManager.play_menu_button_sound()

func start_new_game_with_enhanced_transition():
	"""Spustenie novej hry s vylepšenou tranzíciou"""
	print("🆕 Spúšťam novú hru s enhanced transition")

	# Reset progress pre novú hru
	GameManager.reset_all_progress()

	var tween = create_tween()
	tween.tween_property(self, "modulate", Color(1, 1, 1, 0), 0.5)
	tween.tween_callback(func():
		print("🎬 Spúšťam kapitolu 1")
		GameManager.go_to_chapter(1)
	)

func continue_game_with_enhanced_transition():
	"""Pokračovanie hry s vylepšenou tranzíciou"""
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color(1, 1, 1, 0), 0.5)
	tween.tween_callback(func(): GameManager.continue_game())

func go_to_chapters_with_transition():
	"""Prechod na kapitoly s tranzíciou"""
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color(1, 1, 1, 0), 0.3)
	tween.tween_callback(func(): GameManager.go_to_chapters())

func go_to_settings_with_transition():
	"""Prechod na nastavenia s tranzíciou"""
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color(1, 1, 1, 0), 0.3)
	tween.tween_callback(func(): GameManager.go_to_settings())

func go_to_about_with_transition():
	"""Prechod na o hre s tranzíciou"""
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color(1, 1, 1, 0), 0.3)
	tween.tween_callback(func(): GameManager.go_to_about())

func _input(event):
	"""Spracovanie vstupov"""
	if event.is_action_pressed("ui_cancel"):
		# ESC pre ukončenie hry
		get_tree().quit()
	elif event.is_action_pressed("ui_accept"):
		# Enter pre spustenie fokusovaného tlačidla
		var focused = get_viewport().gui_get_focus_owner()
		if focused and focused is Button:
			focused.pressed.emit()
