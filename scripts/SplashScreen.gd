extends Control

# Splash Screen s animovaným logom pre "Prekliate Dedičstvo"

@onready var logo: TextureRect = $LogoContainer/Logo
@onready var title_label: Label = $TitleContainer/TitleLabel
@onready var loading_label: Label = $LoadingContainer/LoadingLabel
@onready var fade_overlay: ColorRect = $FadeOverlay

# Animačné nastavenia
var splash_duration: float = 3.0
var fade_in_duration: float = 1.0
var fade_out_duration: float = 1.0
var logo_scale_duration: float = 2.0

func _ready():
	print("🎬 Splash Screen spustený")
	
	# Aplikovanie fontov
	apply_fonts()
	
	# Nastavenie počiatočného stavu
	setup_initial_state()
	
	# Spustenie animácie
	start_splash_animation()

func apply_fonts():
	"""Aplikuje fonty na UI elementy"""
	if loading_label:
		# Jednoduchý fallback bez FontLoader
		print("🎨 Aplikujem základné fonty na SplashScreen")

		loading_label.add_theme_color_override("font_color", Color("#D4AF37"))
		loading_label.add_theme_font_size_override("font_size", 20)

		# Pridaj tieň pre lepšiu čitateľnosť
		loading_label.add_theme_color_override("font_shadow_color", Color.BLACK)
		loading_label.add_theme_constant_override("shadow_offset_x", 2)
		loading_label.add_theme_constant_override("shadow_offset_y", 2)

func setup_initial_state():
	"""Nastaví počiatočný stav pre animáciu"""
	# Logo začína neviditeľné ale v normálnej veľkosti
	logo.modulate.a = 0.0
	logo.scale = Vector2(1.0, 1.0)

	# Title label začína neviditeľný
	title_label.modulate.a = 0.0

	# Loading label začína neviditeľný
	loading_label.modulate.a = 0.0

	# Fade overlay začína čierny
	fade_overlay.color.a = 1.0

func start_splash_animation():
	"""Spustí hlavnú animáciu splash screen"""
	print("🎭 Spúšťam splash animáciu")

	# 1. Krátky fade in z čiernej (0.3s)
	var fade_tween = create_tween()
	fade_tween.tween_property(fade_overlay, "color:a", 0.0, 0.3)
	fade_tween.finished.connect(_on_fade_in_finished)

func _on_fade_in_finished():
	# 2. Logo fade in (1.0s) - bez scale animácie
	var logo_tween = create_tween()
	logo_tween.tween_property(logo, "modulate:a", 1.0, fade_in_duration)
	logo_tween.finished.connect(_on_logo_fade_finished)

func _on_logo_fade_finished():
	# 3. Title label fade in (0.5s) - krátko po logo
	var timer = get_tree().create_timer(0.2)
	timer.timeout.connect(_start_title_fade)

func _start_title_fade():
	var title_tween = create_tween()
	title_tween.tween_property(title_label, "modulate:a", 1.0, 0.5)
	title_tween.finished.connect(_on_title_fade_finished)

func _on_title_fade_finished():
	# 4. Logo pulse efekt (jemné zväčšenie a zmenšenie)
	create_logo_pulse_animation()

	# 5. Loading label fade in (0.5s)
	var timer = get_tree().create_timer(0.3)
	timer.timeout.connect(_start_loading_fade)

func _start_loading_fade():
	var loading_tween = create_tween()
	loading_tween.tween_property(loading_label, "modulate:a", 1.0, 0.5)
	loading_tween.finished.connect(_on_loading_fade_finished)

func _on_loading_fade_finished():
	# 5. Loading dots animácia
	start_loading_dots_animation()

	# 6. Čakanie na dokončenie splash duration
	var timer = get_tree().create_timer(splash_duration - 2.0)
	timer.timeout.connect(_start_final_fade)

func _start_final_fade():
	# 7. Fade out a prechod na main menu
	fade_to_main_menu()

func create_logo_pulse_animation():
	"""Vytvorí jemný pulse efekt pre logo"""
	var pulse_tween = create_tween()
	pulse_tween.set_loops()  # Nekonečná slučka
	
	# Jemné zväčšenie a zmenšenie
	pulse_tween.tween_property(logo, "scale", Vector2(1.05, 1.05), 1.0)
	pulse_tween.tween_property(logo, "scale", Vector2(1.0, 1.0), 1.0)

func start_loading_dots_animation():
	"""Animuje loading text s bodkami"""
	var dots_count = 0
	var base_text = "Načítava sa"
	
	# Vytvorenie timer pre dots animáciu
	var timer = Timer.new()
	timer.wait_time = 0.5
	timer.timeout.connect(_on_dots_timer_timeout.bind(timer, base_text))
	add_child(timer)
	timer.start()

func _on_dots_timer_timeout(timer: Timer, base_text: String):
	"""Callback pre dots animáciu"""
	var dots_count = (Time.get_ticks_msec() / 500) % 4
	var dots = ""
	for i in range(dots_count):
		dots += "."
	
	loading_label.text = base_text + dots
	
	# Zastaviť timer po určitom čase
	if Time.get_ticks_msec() > 4000:  # 4 sekundy
		timer.queue_free()

func fade_to_main_menu():
	"""Fade out a prechod na main menu"""
	print("🎬 Prechod na Main Menu")

	# Krátky fade out animácia (0.3s)
	var fade_tween = create_tween()
	fade_tween.set_parallel(true)

	# Logo, title a loading label fade out
	fade_tween.tween_property(logo, "modulate:a", 0.0, 0.3)
	fade_tween.tween_property(title_label, "modulate:a", 0.0, 0.3)
	fade_tween.tween_property(loading_label, "modulate:a", 0.0, 0.3)

	# Fade overlay fade in
	fade_tween.tween_property(fade_overlay, "color:a", 1.0, 0.3)

	fade_tween.finished.connect(_load_main_menu)

func _load_main_menu():
	# Prechod na main menu
	print("🎨 SplashScreen: Prechod na Main Menu")
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _input(event):
	"""Umožní preskočiť splash screen stlačením akéhokoľvek tlačidla"""
	if event.is_pressed():
		print("⏭️ Splash screen preskočený")
		# Okamžitý prechod na main menu
		get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")
